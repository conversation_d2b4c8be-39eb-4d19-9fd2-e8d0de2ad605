import { DOCUMENT, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, HostListener, inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ChildActivationEnd, Data, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { SchemaOrgService, SchemaOrgWebpageDataTemplate, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import { AnalyticsService, DatalayerScrollOptions } from '@trendency/kesma-ui';
import { GoogleTagManagerService } from 'angular-google-tag-manager';
import { asyncScheduler, fromEvent, Observable, Subject, takeUntil } from 'rxjs';
import { buffer, filter, map, tap, throttleTime } from 'rxjs/operators';
import { defaultMetaInfo, UrlService, AuthService } from './shared';
import { ExitPopupComponent } from './shared/components/exit-popup/exit-popup.component';
import { AdvertService } from './shared/services/advert.service';
import { ConditionalScriptLoaderService } from './shared/services/conditional-script-loader.service';

const exitPopupStorageKey = 'exitPopupShown';

declare global {
  interface Window {
    callAdvertScripts: () => void;
    adsDoNotServeAds?: boolean;
    __adsConfig?: any;
    conditionalScriptLoader?: ConditionalScriptLoaderService;
  }
}

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ExitPopupComponent, RouterOutlet, NgIf],
})
export class AppComponent implements OnInit, OnDestroy {
  isShowExitPopup = false;
  private readonly document = inject(DOCUMENT);
  private readonly analyticsScroll = new Subject<DatalayerScrollOptions>();
  private readonly unsubscribe = new Subject<void>();
  private readonly mouseLeave$ = new Subject<void>();
  isInitialLoad = true;

  constructor(
    private readonly seoService: SeoService,
    private readonly utilsService: UtilService,
    private readonly schemaService: SchemaOrgService,
    private readonly router: Router,
    private readonly urlService: UrlService,
    private readonly analyticsService: AnalyticsService,
    private readonly gtmService: GoogleTagManagerService,
    private readonly advertService: AdvertService,
    private readonly authService: AuthService,
    private readonly storageService: StorageService,
    private readonly conditionalScriptLoaderService: ConditionalScriptLoaderService
  ) {
    // Expose the service globally for debugging
    if (this.utilsService.isBrowser()) {
      (window as any).conditionalScriptLoader = this.conditionalScriptLoaderService;
    }
  }

  @HostListener('window:scroll', ['$event'])
  onScroll(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    const pageHeight = this.document.body.scrollHeight - window.innerHeight;
    const scrollPercent = Math.round((window.scrollY / pageHeight) * 100);

    this.analyticsScroll.next({ scrollPercent, pageHeight });
  }

  ngOnInit(): void {
    this.handleExitPopup();
    this.seoService.setMetaData(defaultMetaInfo, { skipSeoMetaCheck: true });
    SchemaOrgWebpageDataTemplate.url = this.seoService.hostUrl;
    if (this.utilsService.isBrowser()) {
      this.setupAnalyticsTracking();
      this.gtmService.addGtmToDom();
    }

    (this.router.events.pipe(filter((event) => event instanceof NavigationEnd)) as Observable<NavigationEnd>).subscribe((event: NavigationEnd): void => {
      this.schemaService.removeStructuredData();
      this.schemaService.insertSchema(SchemaOrgWebpageDataTemplate);

      if (this.isInitialLoad) {
        this.isInitialLoad = false;
        this.handleInitialAdvertLoading();
      } else {
        this.advertService.callAdvertScriptOnNavigation();
        this.advertService.destroyInterval$.next();
      }

      this.urlService.setPreviousUrl(event.url);
    });
    if (this.document.body.classList.contains('ssr')) {
      this.document.body.classList.remove('ssr');
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  private handleInitialAdvertLoading(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    const token = this.authService.getToken();

    if (!token) {
      this.advertService.loadAdvertScriptConditionally(undefined, false);
    } else {
      this.authService
        .isAuthenticated()
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((isAuthenticated: boolean) => {
          const user = this.authService.currentUser;
          this.advertService.loadAdvertScriptConditionally(isAuthenticated ? user : undefined, isAuthenticated);
        });
    }

    this.authService.currentUserSubject.pipe(takeUntil(this.unsubscribe)).subscribe((user) => {
      if (this.advertService.isUserAuthenticationChecked()) {
        this.advertService.handleUserLoginAfterVisit(user);
      }
    });
  }

  private handleExitPopup(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    fromEvent<MouseEvent>(document, 'mouseleave')
      .pipe(
        tap(() => {
          if (!this.storageService.getSessionStorageData(exitPopupStorageKey)) {
            this.storageService.setSessionStorageData(exitPopupStorageKey, 'true');
            this.isShowExitPopup = true;
          } else {
            this.mouseLeave$.next();
            this.mouseLeave$.complete();
          }
        }),
        takeUntil(this.mouseLeave$)
      )
      .subscribe();
  }

  private setupAnalyticsTracking(): void {
    // Navigation end used to trigger gtag
    const navigationEnd$: Observable<NavigationEnd> = (this.router.events.pipe(filter((e) => e instanceof NavigationEnd)) as Observable<NavigationEnd>).pipe();

    // Child activationEnd to get the leaf route data in order to see if we send pageViews there.
    (this.router.events.pipe(takeUntil(this.unsubscribe)).pipe(filter((e) => e instanceof ChildActivationEnd)) as Observable<ChildActivationEnd>)
      .pipe(
        // ChildActivationEnd triggers for every path activation, we need only the leaf so after navigation end
        // we get all of the childActivationEnd events and we only need the first one.
        buffer(navigationEnd$),
        map(([leafNode]: ChildActivationEnd[]) => leafNode?.snapshot?.firstChild?.data),
        map((data?: Data & { omitGlobalPageView?: boolean }) => !data?.omitGlobalPageView)
      )
      .subscribe((shouldSendGlobalAnalytics: boolean) => {
        console.log('Should send global analytics: ', shouldSendGlobalAnalytics);
        if (shouldSendGlobalAnalytics) {
          setTimeout(() => {
            this.analyticsService.sendPageView();
          }, 100);
        }
      });

    this.analyticsScroll
      .pipe(takeUntil(this.unsubscribe))
      .pipe(throttleTime(2000, asyncScheduler, { trailing: true }))
      .subscribe((options) => this.sendAnalyticsScrollEvent(options));
  }

  private sendAnalyticsScrollEvent(options: DatalayerScrollOptions): void {
    this.analyticsService.sendScrollEvent(options);
  }
}
