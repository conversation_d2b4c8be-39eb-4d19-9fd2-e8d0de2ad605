import { DOCUMENT } from '@angular/common';
import { Inject, Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import { filter } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class ConditionalScriptLoaderService {
  private readonly targetUrl = '/hirek/2008/04/trackback-teszteles';
  private scriptsLoaded = false;

  constructor(
    private readonly router: Router,
    private readonly utilsService: UtilService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {
    this.initializeRouterListener();
  }

  private initializeRouterListener(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => {
      this.handleNavigation(event.url);
    });

    this.handleNavigation(this.router.url);
  }

  private handleNavigation(url: string): void {
    console.debug('[ConditionalScriptLoader] Navigation to:', url, 'Target:', this.targetUrl);

    if (url === this.targetUrl) {
      console.debug('[ConditionalScriptLoader] Target page detected, loading scripts');
      this.loadScripts();
    } else {
      console.debug('[ConditionalScriptLoader] Not target page, removing scripts');
      this.removeScripts();
    }
  }

  private loadScripts(): void {
    if (this.scriptsLoaded || !this.utilsService.isBrowser()) {
      return;
    }

    const existingHtmlLoadScript = this.document.getElementById('tQNIlYwsPFK');
    const existingLoaderScript = this.document.querySelector('script[src="/assets/scripts/html-load-loader.js"]');

    if (existingHtmlLoadScript && existingLoaderScript) {
      this.scriptsLoaded = true;
      return;
    }

    if (!existingHtmlLoadScript) {
      const htmlLoadScript = this.document.createElement('script');
      htmlLoadScript.async = true;
      htmlLoadScript.id = 'tQNIlYwsPFK';
      htmlLoadScript.setAttribute('data-sdk', 'l/1.1.11');
      htmlLoadScript.setAttribute('data-cfasync', 'false');
      htmlLoadScript.src = 'https://html-load.com/loader.min.js';
      htmlLoadScript.setAttribute('charset', 'UTF-8');
      htmlLoadScript.setAttribute('data', 'irrron6o4fhugojo4o2o4hch8hto8jhe2h4h8oso7o4ojfogh8h4oj5h4h4fo7o4hefogovh4oso7o4fh4oifuhyojojo7o4qhcuh8hto8jqo4kh9');
      this.document.head.appendChild(htmlLoadScript);
    }

    if (!existingLoaderScript) {
      const loaderScript = this.document.createElement('script');
      loaderScript.src = '/assets/scripts/html-load-loader.js';
      this.document.head.appendChild(loaderScript);
    }

    this.scriptsLoaded = true;
    console.debug('[ConditionalScriptLoader] Scripts loaded for target page:', this.targetUrl);
  }

  private removeScripts(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    // Remove the html-load.com script
    const htmlLoadScript = this.document.getElementById('tQNIlYwsPFK');
    if (htmlLoadScript) {
      htmlLoadScript.remove();
      console.debug('[ConditionalScriptLoader] Removed html-load script');
    }

    const loaderScripts = this.document.querySelectorAll('script[src="/assets/scripts/html-load-loader.js"]');
    loaderScripts.forEach((script) => {
      script.remove();
      console.debug('[ConditionalScriptLoader] Removed loader script');
    });

    const duplicateScripts = this.document.querySelectorAll('script[id^="tQNIlYwsPFK"]');
    duplicateScripts.forEach((script) => script.remove());

    this.scriptsLoaded = false;
  }
}
