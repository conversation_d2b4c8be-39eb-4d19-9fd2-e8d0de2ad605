import { DOCUMENT } from '@angular/common';
import { Inject, Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import { filter } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class ConditionalScriptLoaderService {
  private readonly targetUrl = '/hirek/2008/04/trackback-teszteles';
  private scriptsLoaded = false;
  private renderer: Renderer2;

  constructor(
    private readonly router: Router,
    private readonly utilsService: UtilService,
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly rendererFactory: RendererFactory2
  ) {
    this.renderer = this.rendererFactory.createRenderer(null, null);
    this.initializeRouterListener();
  }

  private initializeRouterListener(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => {
      this.handleNavigation(event.url);
    });

    this.handleNavigation(this.router.url);
  }

  private handleNavigation(url: string): void {
    console.debug('[ConditionalScriptLoader] Navigation to:', url, 'Target:', this.targetUrl);

    if (url === this.targetUrl) {
      console.debug('[ConditionalScriptLoader] Target page detected, loading scripts');
      this.loadScripts();
    } else {
      console.debug('[ConditionalScriptLoader] Not target page, removing scripts');
      this.removeScripts();
    }
  }

  private loadScripts(): void {
    if (this.scriptsLoaded || !this.utilsService.isBrowser()) {
      return;
    }

    const existingHtmlLoadScript = this.document.getElementById('tQNIlYwsPFK');
    const existingLoaderScript = this.document.querySelector('script[src="/assets/scripts/html-load-loader.js"]');

    if (existingHtmlLoadScript && existingLoaderScript) {
      this.scriptsLoaded = true;
      return;
    }

    if (!existingHtmlLoadScript) {
      console.log('[ConditionalScriptLoader] Creating script using Angular Renderer2');

      // Use Angular's Renderer2 for safe DOM manipulation
      const htmlLoadScript = this.renderer.createElement('script');

      // Set attributes using Renderer2
      this.renderer.setAttribute(htmlLoadScript, 'async', 'true');
      this.renderer.setAttribute(htmlLoadScript, 'id', 'tQNIlYwsPFK');
      this.renderer.setAttribute(htmlLoadScript, 'data-sdk', 'l/1.1.11');
      this.renderer.setAttribute(htmlLoadScript, 'data-cfasync', 'false');
      this.renderer.setAttribute(htmlLoadScript, 'src', 'https://html-load.com/loader.min.js');
      this.renderer.setAttribute(htmlLoadScript, 'charset', 'UTF-8');
      this.renderer.setAttribute(
        htmlLoadScript,
        'data',
        'irrron6o4fhugojo4o2o4hch8hto8jhe2h4h8oso7o4ojfogh8h4oj5h4h4fo7o4hefogovh4oso7o4fh4oifuhyojojo7o4qhcuh8hto8jqo4kh9'
      );
      // Add event listeners using Renderer2
      this.renderer.listen(htmlLoadScript, 'load', () => {
        console.log('[ConditionalScriptLoader] html-load script loaded successfully');
      });

      this.renderer.listen(htmlLoadScript, 'error', (error) => {
        console.error('[ConditionalScriptLoader] Error loading html-load script:', error);
      });

      console.log('[ConditionalScriptLoader] About to append script to head using Renderer2');
      console.log('[ConditionalScriptLoader] Script element:', htmlLoadScript);

      // Append using Renderer2
      this.renderer.appendChild(this.document.head, htmlLoadScript);

      console.log('[ConditionalScriptLoader] Script appended to head using Renderer2');

      // Check if script is actually in the DOM after appending
      setTimeout(() => {
        const checkScript = this.document.getElementById('tQNIlYwsPFK');
        console.log('[ConditionalScriptLoader] Script check after 1s:', checkScript ? 'FOUND' : 'NOT FOUND');
        if (checkScript) {
          console.log('[ConditionalScriptLoader] Script still in DOM:', checkScript);
        }

        // Also check using querySelector
        const checkScript2 = this.document.querySelector('script[id="tQNIlYwsPFK"]');
        console.log('[ConditionalScriptLoader] Script check via querySelector:', checkScript2 ? 'FOUND' : 'NOT FOUND');

        // Check all scripts in head
        const allScripts = this.document.head.querySelectorAll('script');
        console.log('[ConditionalScriptLoader] Total scripts in head after append:', allScripts.length);
      }, 1000);
    }

    if (!existingLoaderScript) {
      console.log('[ConditionalScriptLoader] Creating loader script using Angular Renderer2');

      const loaderScript = this.renderer.createElement('script');
      this.renderer.setAttribute(loaderScript, 'src', '/assets/scripts/html-load-loader.js');

      console.log('[ConditionalScriptLoader] About to append loader script using Renderer2');
      this.renderer.appendChild(this.document.head, loaderScript);
      console.log('[ConditionalScriptLoader] Loader script appended using Renderer2');
    }

    this.scriptsLoaded = true;
    console.debug('[ConditionalScriptLoader] Scripts loaded for target page:', this.targetUrl);
  }

  private removeScripts(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    // Remove the html-load.com script
    const htmlLoadScript = this.document.getElementById('tQNIlYwsPFK');
    if (htmlLoadScript) {
      htmlLoadScript.remove();
      console.debug('[ConditionalScriptLoader] Removed html-load script');
    }

    const loaderScripts = this.document.querySelectorAll('script[src="/assets/scripts/html-load-loader.js"]');
    loaderScripts.forEach((script) => {
      script.remove();
      console.debug('[ConditionalScriptLoader] Removed loader script');
    });

    const duplicateScripts = this.document.querySelectorAll('script[id^="tQNIlYwsPFK"]');
    duplicateScripts.forEach((script) => script.remove());

    this.scriptsLoaded = false;
  }

  // Debug method to check script status - can be called from browser console
  public debugScriptStatus(): void {
    console.log('[ConditionalScriptLoader] === DEBUG STATUS ===');
    console.log('[ConditionalScriptLoader] Current URL:', this.router.url);
    console.log('[ConditionalScriptLoader] Target URL:', this.targetUrl);
    console.log('[ConditionalScriptLoader] Scripts loaded flag:', this.scriptsLoaded);
    console.log('[ConditionalScriptLoader] Is browser:', this.utilsService.isBrowser());

    const htmlLoadScript = this.document.getElementById('tQNIlYwsPFK');
    const loaderScript = this.document.querySelector('script[src="/assets/scripts/html-load-loader.js"]');

    console.log('[ConditionalScriptLoader] HTML Load script in DOM:', htmlLoadScript ? 'YES' : 'NO');
    console.log('[ConditionalScriptLoader] Loader script in DOM:', loaderScript ? 'YES' : 'NO');

    if (htmlLoadScript) {
      console.log('[ConditionalScriptLoader] HTML Load script element:', htmlLoadScript);
    }
    if (loaderScript) {
      console.log('[ConditionalScriptLoader] Loader script element:', loaderScript);
    }

    // Check all scripts in head
    const allScripts = this.document.head.querySelectorAll('script');
    console.log('[ConditionalScriptLoader] Total scripts in head:', allScripts.length);
    allScripts.forEach((script, index) => {
      console.log(`[ConditionalScriptLoader] Script ${index}:`, script.src || script.innerHTML.substring(0, 50));
    });
  }
}
