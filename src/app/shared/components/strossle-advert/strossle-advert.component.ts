import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, Input, OnDestroy, OnInit } from '@angular/core';
import { AsyncPipe, NgIf } from '@angular/common';
import { AdvertService } from '../../services/advert.service';
import { Observable, Subject, takeUntil } from 'rxjs';
import { StorageService } from '@trendency/kesma-core';

@Component({
  selector: 'app-strossle-advert',
  templateUrl: './strossle-advert.component.html',
  styleUrls: ['./strossle-advert.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe],
})
export class StrossleAdvertComponent implements OnInit, OnDestroy {
  @Input() advertId: string;
  hasDebug: boolean;
  isAdEnabled$: Observable<boolean>;
  isAdEnabled: boolean = true;
  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly advertService: AdvertService,
    private readonly elementRef: ElementRef,
    private readonly cdr: ChangeDetectorRef,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.hasDebug = this.storageService.getLocalStorageData('apptest') === 'true';
    this.isAdEnabled$ = this.advertService.enableAdsSubject.asObservable();

    this.advertService.enableAdsSubject.pipe(takeUntil(this.destroy$)).subscribe((isEnabled: boolean) => {
      this.isAdEnabled = isEnabled;

      if (!isEnabled) {
        this.hideAndClearAdContent();
      }

      this.cdr.detectChanges();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Forcefully hides the component and clears any ad content that might have been injected
   */
  private hideAndClearAdContent(): void {
    const element = this.elementRef.nativeElement;

    if (element) {
      element.style.display = 'none';

      const adContainer = element.querySelector(`#${this.advertId}`);
      if (adContainer) {
        adContainer.innerHTML = '';

        const scripts = adContainer.querySelectorAll('script');
        scripts.forEach((script: Element) => script.remove());

        const iframes = adContainer.querySelectorAll('iframe');
        iframes.forEach((iframe: Element) => iframe.remove());

        (adContainer as HTMLElement).style.display = 'none';
      }

      const adElements = element.querySelectorAll('[data-google-query-id], [id*="google_ads_iframe"], iframe[src*="doubleclick"], iframe[src*="publisher1st"]');
      adElements.forEach((adElement: Element) => adElement.remove());
    }
  }
}
