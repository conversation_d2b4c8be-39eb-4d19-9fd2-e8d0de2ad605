<div class="poll" *ngIf="vm.state$ | async as state">
  <ng-container *ngIf="hostClass === votingStyle.DEFAULT; else grayStyleHeader">
    <div class="poll-header">
      <img class="poll-header-icon" src="assets/images/icons/szavazas.svg" width="49" height="49" loading="lazy" alt="Szavazás ikon" />
      <div class="poll-header-title">{{ data?.title }}</div>
      <div class="poll-header-question">{{ data?.question }}</div>
    </div>
  </ng-container>

  <ng-template #grayStyleHeader>
    <div class="poll-header">
      <div class="poll-header-question">
        <span>{{ data?.question?.slice(0, -1) }}</span>
        <i class="icon icon-mandiner-question-mark"></i>
      </div>
    </div>
  </ng-template>

  <form class="poll-form">
    @if (state.showResults && !state.voteData.isResultVisible) {
      <h2><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy sza<PERSON>ztál!</h2>
    } @else {
      <ng-container *ngIf="hostClass === votingStyle.DEFAULT; else grayStylePollFormWrapper">
        <ul class="poll-form-wrapper">
          <ng-container *ngFor="let item of data?.answers">
            <li class="poll-form-radio" (click)="setVoteId(item.id)" [ngClass]="{ active: state.userVotedId === item.id, results: state.showResults }">
              <div class="poll-form-radio-option">
                <div *ngIf="state.showResults" class="poll-form-result">{{ item?.votePercentage ?? '0' }}%</div>
                <label class="poll-form-radio-label" [for]="item.id">
                  {{ item.answer }}
                  <input
                    class="poll-form-radio-input"
                    type="radio"
                    [name]="'poll-' + data?.id"
                    [id]="item.id"
                    [value]="item.id"
                    [disabled]="state.showResults"
                    [checked]="state.userVotedId === item.id"
                  />
                  <span *ngIf="!state.showResults" class="poll-form-checkmark"></span> </label
                ><br />
              </div>
              <div *ngIf="state.showResults" [style.width]="item.votePercentage + '%'" class="poll-form-radio-progress-bar"></div>
            </li>
          </ng-container>
        </ul>
      </ng-container>
    }

    <ng-template #grayStylePollFormWrapper>
      <ul class="poll-form-wrapper">
        <ng-container *ngFor="let item of data?.answers">
          <li class="poll-form-radio" (click)="setVoteId(item.id)" [ngClass]="{ active: state.userVotedId === item.id, results: state.showResults }">
            <div class="poll-form-radio-option">
              <label class="poll-form-radio-label" [for]="item.id">
                {{ item.answer }}
                <input
                  class="poll-form-radio-input"
                  type="radio"
                  name="poll"
                  [id]="item.id"
                  [value]="item.id"
                  [disabled]="state.showResults"
                  [checked]="state.userVotedId === item.id"
                />
                <span *ngIf="!state.showResults" class="poll-form-checkmark"></span> </label
              ><br />
            </div>
            <ng-container *ngIf="state.showResults">
              <div
                [style.width]="item.votePercentage + '%'"
                class="poll-form-radio-progress-bar"
                [ngClass]="{ highest: isThisTheHighestVoteCount(item?.voteCount) }"
              ></div>
              <span class="poll-form-radio-vote-count">{{ item?.voteCount }} szavazat</span>
            </ng-container>
          </li>
        </ng-container>
      </ul>
    </ng-template>

    <ng-container *ngIf="hostClass === votingStyle.DEFAULT; else grayStyleButtons">
      <div class="poll-form-buttons">
        <input
          [ngClass]="{ disabled: state.isSubmitButtonDisabled || isExpired, voted: state.showResults }"
          class="poll-form-buttons-submit"
          type="submit"
          [value]="state.buttonLabel"
          [disabled]="state.isSubmitButtonDisabled || isExpired"
          (click)="onVote()"
        />
      </div>
    </ng-container>

    <ng-template #grayStyleButtons>
      <div class="poll-form-buttons">
        <input
          [ngClass]="{ disabled: state.isSubmitButtonDisabled || isExpired }"
          class="poll-form-buttons-submit"
          type="submit"
          [value]="state.buttonLabel"
          [disabled]="state.isSubmitButtonDisabled || isExpired"
          (click)="onVote()"
        />
      </div>
    </ng-template>
  </form>
</div>
