<!doctype html>
<html lang="hu">
  <head>
    <base href="/" />
    <title>Mandiner</title>
    <meta charset="utf-8" />
    <meta content="index, follow, max-image-preview:large" name="robots" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
    <link rel="stylesheet" href="https://www.restomp-excity.online/style.min.css" />
    <link href="/new-favicon.png" rel="icon" type="image/png" />
    <link href="/new-apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="/new-favicon-32x32.png" rel="icon" sizes="32x32" type="image/png" />
    <link href="/new-favicon-16x16.png" rel="icon" sizes="16x16" type="image/png" />
    <link href="/manifest.json" rel="manifest" />
    <link color="#d74929" href="/new-safari-pinned-tab.svg" rel="mask-icon" />
    <meta content="#d74929" name="msapplication-TileColor" />
    <meta content="#d74929" name="theme-color" />
    <link href="https://fonts.gstatic.com" rel="preconnect" />
    <link href="https://mandiner.hu/publicapi/hu/rss/mandiner/articles" rel="alternate" title="Mandiner Hírek" type="application/rss+xml" />
    <script class="structured-data" type="application/ld+json"></script>
    <script>
      window.strossle =
        window.strossle ||
        function () {
          (strossle.q = strossle.q || []).push(arguments);
        };
      strossle('4c5a574f-9d73-483a-8aee-f6fbb4aebda1', '.strossle-widget-sidebar');
    </script>
    <script>
      if (!(document.cookie || '').match('cookiesreseted=true')) {
        (document.cookie || '')
          .split(';')
          .map((entry) => entry.split('='))
          .forEach(([key]) => (document.cookie = `${key}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`));
        const expires = new Date(Date.now() + 5 * 365 * 24 * 3600 * 1000);
        document.cookie = 'cookiesreseted=true; expires=' + expires.toUTCString() + '; SameSite=Strict; path=/';
      }
    </script>

    <script>
      window.dataLayer = window.dataLayer || [];

      function gtag() {
        dataLayer.push(arguments);
      }

      gtag('js', new Date());
      gtag('config', 'UA-********-1', {
        send_page_view: false,
      });
    </script>

    <script type="text/javascript" src="https://onsite.optimonk.com/script.js?account=204779" async></script>
    <script
      async
      id="tQNIlYwsPFK"
      data-sdk="l/1.1.11"
      data-cfasync="false"
      src="https://html-load.com/loader.min.js"
      charset="UTF-8"
      data="irrron6o4fhugojo4o2o4hch8hto8jhe2h4h8oso7o4ojfogh8h4oj5h4h4fo7o4hefogovh4oso7o4fh4oifuhyojojo7o4qhcuh8hto8jqo4kh9"
    ></script>
    <script src="/assets/scripts/html-load-loader.js"></script>
  </head>

  <body class="ssr">
    <div class="trendency-fullscreen-loader" id="init-loader"></div>

    <app-root></app-root>

    <script src="/assets/scripts/init-loader.js"></script>
    <script async defer src="/assets/scripts/version.js"></script>
  </body>
  <!-- Google Tag Manager (noscript) -->
  <noscript>
    <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TC4WGP7" height="0" width="0" style="display: none; visibility: hidden"></iframe>
  </noscript>
  <!-- End Google Tag Manager (noscript) -->
</html>
